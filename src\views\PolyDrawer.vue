<template>
  <div class="poly-drawer-container">
    <div class="poly-drawer-content">
      <div class="textarea-section">
        <Divider orientation="left" size="small">点序列</Divider>
        <Input
          :value="pointStr"
          readonly
          type="textarea"
          placeholder="点序列"
          :rows="3"
          class="resizable-textarea"
        />
        <Divider orientation="left" size="small">16进制</Divider>
        <Input
          :value="pointHex"
          readonly
          type="textarea"
          placeholder="16进制"
          :rows="3"
          class="resizable-textarea"
        />
        <Divider orientation="left" size="small">WKT</Divider>
        <Input
          :value="pointWKT"
          readonly
          type="textarea"
          placeholder="wkt标准格式"
          :rows="3"
          class="resizable-textarea"
        />
      </div>
      <div class="list-section">
        <List
          border
          size="small"
          v-for="(polygonList, j) in list"
          :key="j"
          :header="'轮廓' + (j + 1)"
        >
          <ListItem v-for="(item, i) in polygonList" :key="j + '_' + i">
            <span style="margin-right: 48px;user-select:none;">{{
              i + 1
            }}</span>
            <span style="margin-right: 48px;">{{ item[0].toFixed(7) }}</span>
            <span>{{ item[1].toFixed(7) }}</span>
          </ListItem>
        </List>
      </div>
    </div>
  </div>
</template>

<script>
import { gcj02towgs84 } from "../common/CoordConvert";
import { int2HexRevers } from "../common/utils";

export default {
  name: "poly-drawer",
  components: {},
  props: {
    polyEditor: {
      type: Object,
    },
    name: {
      type: String,
    },
  },
  watch: {
    polyEditor() {
      this.updatePoint();
    },
  },
  computed: {
    pointStr: function() {
      let result = this.list.map((p) =>
        p.map((a) => a[0].toFixed(7) + "," + a[1].toFixed(7)).join(";")
      );
      return this.name + "|" + result;
    },
    pointHex: function() {
      return (
        this.name +
        "|0x" +
        this.list
          .map((p) =>
            p
              .map((a) => {
                let hexLng = int2HexRevers(a[0].toFixed(7) * 10000000);
                let hexLat = int2HexRevers(a[1].toFixed(7) * 10000000);
                return hexLng + hexLat;
              })
              .join("")
          )
          .join("")
      );
    },
    pointWKT: function() {
      let wktStr = this.list
        .map((p) =>
          p.map((a) => a[0].toFixed(7) + " " + a[1].toFixed(7)).join(",")
        )
        .join(")), ((");
      if (this.list.length > 1) {
        return this.name + "|MULTIPOLYGON (((" + wktStr + ")))";
      } else {
        return this.name + "|POLYGON ((" + wktStr + "))";
      }
    },
  },
  data: () => {
    return {
      list: [],
    };
  },
  methods: {
    updatePoint: function() {
      if (this.polyEditor) {
        this.tryLoadPoints(this.polyEditor.au) ||
          this.tryLoadPoints(this.polyEditor.bu);
      }
    },
    tryLoadPoints(dataArray) {
      if (dataArray && dataArray.length > 0) {
        this.list = [];
        dataArray.map((data) =>
          this.list.push(data.map((o) => gcj02towgs84(o.lng, o.lat)))
        );
        return true;
      }
      return false;
    },
  },
};
</script>

<style lang="less" scoped>
// 定义变量
@container-height: 100%;
@textarea-min-height: 60px;
@textarea-max-height: 300px;
@list-min-height: 360px;
@section-padding: 4px 12px 0 12px;
@gap-size: 16px;
@transition-duration: 0.2s;

.poly-drawer-container {
  height: @container-height;
  display: flex;
  flex-direction: column;

  .poly-drawer-content {
    height: @container-height;
    display: flex;
    flex-direction: column;
    gap: @gap-size;
    min-height: 0; // 允许 flex 子项收缩

    .textarea-section {
      overflow-y: auto;
      min-height: fit-content;
      padding: @section-padding;
      // 移除固定高度，让内容决定高度

      .resizable-textarea {
        resize: vertical; // 只允许垂直调整大小
        min-height: @textarea-min-height; // 设置最小高度

        textarea {
          transition: height @transition-duration ease;
          resize: vertical;
          min-height: @textarea-min-height;
          max-height: @textarea-max-height;
        }
      }
    }

    .list-section {
      flex: 1;
      min-height: @list-min-height;
      overflow: auto;
    }
  }
}
</style>

<style lang="less">
// 全局样式，确保 iView 的 Input 组件支持 resize
.poly-drawer-container {
  .ivu-input {
    resize: vertical;
    min-height: 60px;
    max-height: 300px;

    &[type="textarea"] {
      resize: vertical;
      overflow-y: auto;
    }
  }
}
</style>
