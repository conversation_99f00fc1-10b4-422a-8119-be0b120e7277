import Vue from "vue";
import Vuex from "vuex";
import /*loginApi logoutApi*/ "../api";

Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    // // 用户认证状态
    // isLoggedIn: false,
    // // 用户信息
    // user: null,
    // // 访问令牌
    // token: null,
    isLoggedIn: true,
    user: {
      id: 1,
      username: "user",
      name: "账号",
      amapKey: "ba50cb6ffe332f33d73cd68d2234a25c",
    },
    token: 666,
  },

  getters: {
    isLoggedIn: (state) => state.isLoggedIn,
    user: (state) => state.user,
    token: (state) => state.token,
  },

  mutations: {
    // 设置登录状态
    SET_LOGIN_STATUS(state, status) {
      state.isLoggedIn = status;
    },

    // 设置用户信息
    SET_USER(state, user) {
      state.user = user;
    },

    // 设置token
    SET_TOKEN(state, token) {
      state.token = token;
    },

    // 清除所有用户数据
    CLEAR_USER_DATA(state) {
      state.isLoggedIn = false;
      state.user = null;
      state.token = null;
    },
  },

  actions: {
    // 登录操作
    async login({ commit }, { username, password, captcha, rememberMe }) {
      try {
        let response;

        const apiResponse = await mockLoginApi(username, password, captcha);
        // const apiResponse = await loginApi(username, password, captcha);
        if (apiResponse.data && apiResponse.data.token) {
          response = {
            success: true,
            data: apiResponse.data,
          };
        } else {
          response = {
            success: false,
            message: "登录失败",
          };
        }

        if (response.success) {
          const { user, token } = response.data;

          // 提交mutations更新状态
          commit("SET_LOGIN_STATUS", true);
          commit("SET_USER", user);
          commit("SET_TOKEN", token);

          // 如果选择记住我，将token保存到localStorage
          if (rememberMe) {
            localStorage.setItem("token", token);
            localStorage.setItem("user", JSON.stringify(user));
          } else {
            // 否则保存到sessionStorage
            sessionStorage.setItem("token", token);
            sessionStorage.setItem("user", JSON.stringify(user));
          }

          return response;
        } else {
          throw new Error(response.message || "登录失败");
        }
      } catch (error) {
        throw error;
      }
    },

    // 退出登录
    logout({ commit }) {
      // 清除状态
      commit("CLEAR_USER_DATA");

      // 清除本地存储
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      sessionStorage.removeItem("token");
      sessionStorage.removeItem("user");
    },

    // 初始化用户状态（从本地存储恢复）
    initializeAuth({ commit }) {
      const token =
        localStorage.getItem("token") || sessionStorage.getItem("token");
      const userStr =
        localStorage.getItem("user") || sessionStorage.getItem("user");

      if (token && userStr) {
        try {
          const user = JSON.parse(userStr);
          commit("SET_LOGIN_STATUS", true);
          commit("SET_USER", user);
          commit("SET_TOKEN", token);
        } catch (error) {
          window.console.error("解析用户信息失败:", error);
          // 如果解析失败，清除所有数据
          commit("CLEAR_USER_DATA");
          localStorage.removeItem("token");
          localStorage.removeItem("user");
          sessionStorage.removeItem("token");
          sessionStorage.removeItem("user");
        }
      }
    },

    // 更新用户信息
    updateUser({ commit }, userData) {
      commit("SET_USER", userData);

      // 同步更新本地存储
      if (localStorage.getItem("token")) {
        localStorage.setItem("user", JSON.stringify(userData));
      } else if (sessionStorage.getItem("token")) {
        sessionStorage.setItem("user", JSON.stringify(userData));
      }
    },
  },

  modules: {},
});

// 模拟登录API
function mockLoginApi(username, password, captcha) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 简单的验证码验证（实际项目中应该在后端验证）
      if (!captcha || captcha.length < 4) {
        resolve({
          success: false,
          message: "请输入正确的验证码",
        });
        return;
      }

      // 模拟登录验证 - 使用符合复杂度要求的密码
      if (username === "admin" && password === "Admin123") {
        resolve({
          success: true,
          data: {
            user: {
              id: 1,
              username: "admin",
              name: "管理员",
              amapKey: "ba50cb6ffe332f33d73cd68d2234a25c",
            },
            token: "mock-jwt-token-" + Date.now(),
          },
        });
      } else if (username === "user" && password === "User1234") {
        // 首次用户
        resolve({
          success: true,
          data: {
            user: {
              id: 2,
              username: "user",
              name: "普通用户",
              amapKey: "",
            },
            token: "mock-jwt-token-" + Date.now(),
          },
        });
      } else {
        resolve({
          success: false,
          message: "用户名或密码错误",
        });
      }
    }, 1000); // 模拟网络延迟
  });
}
