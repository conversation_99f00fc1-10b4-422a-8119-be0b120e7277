<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-header">
        <h2>用户登录</h2>
        <div class="test-account-hint">
          <span>目前未对接口，仅测试账号通过(验证码随便输入4到6位)</span>
          <span>已绑定用户 - 账号：admin 密码：Admin123</span>
          <span>首次用户 - 账号：user 密码：User1234</span>
        </div>
      </div>

      <Form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        @submit.native.prevent
      >
        <FormItem prop="username">
          <Input
            v-model.trim="loginForm.username"
            prefix="md-person"
            placeholder="请输入用户名"
            size="large"
            @on-enter="handleLogin"
          />
        </FormItem>

        <FormItem prop="password">
          <Input
            v-model.trim="loginForm.password"
            type="password"
            password
            prefix="md-lock"
            placeholder="请输入密码"
            size="large"
            @on-enter="handleLogin"
          />
        </FormItem>

        <FormItem prop="captcha">
          <div class="captcha-container">
            <Input
              v-model.trim="loginForm.captcha"
              prefix="md-alert"
              placeholder="请输入验证码"
              size="large"
              class="captcha-input"
              @on-enter="handleLogin"
            />
            <div class="captcha-image" @click="refreshCaptcha">
              <img :src="captchaUrl" alt="验证码" />
              <div class="refresh-hint">点击刷新</div>
            </div>
          </div>
        </FormItem>

        <FormItem>
          <Checkbox v-model="loginForm.rememberMe">记住我</Checkbox>
        </FormItem>

        <FormItem>
          <Button
            type="primary"
            size="large"
            long
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? "登录中..." : "登录" }}
          </Button>
        </FormItem>
      </Form>
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";

export default {
  name: "LoginPage",
  data() {
    return {
      loading: false,
      loginForm: {
        username: "",
        password: "",
        captcha: "",
        rememberMe: false,
      },
      captchaUrl: "",
      loginRules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          {
            min: 3,
            max: 20,
            message: "用户名长度在3到20个字符",
            trigger: "blur",
          },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          {
            min: 8,
            max: 20,
            message: "密码长度在8到20个字符",
            trigger: "blur",
          },
          {
            validator: this.validatePassword,
            trigger: "blur",
          },
        ],
        captcha: [
          { required: true, message: "请输入验证码", trigger: "blur" },
          {
            min: 4,
            max: 6,
            message: "验证码长度在4到6个字符",
            trigger: "blur",
          },
        ],
      },
    };
  },
  computed: {
    // 实时检查密码强度
    passwordChecks() {
      const password = this.loginForm.password;
      return {
        length: password && password.length >= 8,
        uppercase: password && /[A-Z]/.test(password),
        lowercase: password && /[a-z]/.test(password),
        number: password && /\d/.test(password),
      };
    },
  },
  mounted() {
    // 如果已经登录，直接跳转到首页
    if (this.$store.getters.isLoggedIn) {
      this.$router.push("/");
    }
    // 初始化验证码
    this.refreshCaptcha();
  },
  methods: {
    ...mapActions(["login"]),

    // 密码复杂度验证
    validatePassword(rule, value, callback) {
      // rule参数由iView表单验证器提供，这里不需要使用
      if (!value) {
        callback();
        return;
      }

      // 检查是否包含大写字母
      const hasUpperCase = /[A-Z]/.test(value);
      // 检查是否包含小写字母
      const hasLowerCase = /[a-z]/.test(value);
      // 检查是否包含数字
      const hasNumber = /\d/.test(value);

      if (!hasUpperCase) {
        callback(new Error("密码必须包含大写字母"));
        return;
      }

      if (!hasLowerCase) {
        callback(new Error("密码必须包含小写字母"));
        return;
      }

      if (!hasNumber) {
        callback(new Error("密码必须包含数字"));
        return;
      }

      callback();
    },

    // 刷新验证码
    refreshCaptcha() {
      // 生成随机时间戳避免缓存
      const timestamp = new Date().getTime();
      window.console.log("刷新验证码");

      // 这里可以替换为真实的验证码API地址
      this.captchaUrl = `/captcha?t=${timestamp}`;

      // 模拟验证码图片
      //   this.captchaUrl = this.generateMockCaptcha();

      // 清空验证码输入
      this.loginForm.captcha = "";
    },

    // 生成模拟验证码图片（base64）
    generateMockCaptcha() {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      canvas.width = 120;
      canvas.height = 40;

      // 背景
      ctx.fillStyle = "#f0f0f0";
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // 生成随机验证码
      const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
      let captchaText = "";
      for (let i = 0; i < 4; i++) {
        captchaText += chars.charAt(Math.floor(Math.random() * chars.length));
      }

      // 绘制验证码文字
      ctx.fillStyle = "#333";
      ctx.font = "20px Arial";
      ctx.textAlign = "center";
      ctx.fillText(captchaText, canvas.width / 2, canvas.height / 2 + 7);

      // 添加干扰线
      for (let i = 0; i < 3; i++) {
        ctx.strokeStyle = `rgb(${Math.floor(Math.random() * 255)}, ${Math.floor(
          Math.random() * 255
        )}, ${Math.floor(Math.random() * 255)})`;
        ctx.beginPath();
        ctx.moveTo(Math.random() * canvas.width, Math.random() * canvas.height);
        ctx.lineTo(Math.random() * canvas.width, Math.random() * canvas.height);
        ctx.stroke();
      }

      return canvas.toDataURL();
    },

    async handleLogin() {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          try {
            await this.login({
              username: this.loginForm.username,
              password: this.loginForm.password,
              captcha: this.loginForm.captcha,
              rememberMe: this.loginForm.rememberMe,
            });

            this.$Message.success("登录成功");

            // 获取重定向路径，如果没有则跳转到首页
            const redirect = this.$route.query.redirect || "/";
            this.$router.push(redirect);
          } catch (error) {
            this.$Message.error(
              error.message || "登录失败，请检查用户名和密码"
            );
            // 登录失败时刷新验证码
            this.refreshCaptcha();
          } finally {
            this.loading = false;
          }
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.login-page {
  min-height: 100vh;
  background-image: url("../assets/login-bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

.login-container {
  position: absolute;
  top: 50%;
  right: 16%;
  transform: translateY(-50%);
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
  height: 500px;
}

.login-header {
  text-align: center;

  h2 {
    font-weight: 500;
    font-size: 40px;
    color: #294558;
    margin-bottom: 16px;
  }

  p {
    color: #666;
    margin: 0;
    font-size: 14px;
  }
}

.test-account-hint {
  display: flex;
  flex-direction: column;
}

.ivu-form-item {
  margin-bottom: 20px;
}

.ivu-btn {
  margin-top: 10px;
}

.captcha-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  position: relative;
  cursor: pointer;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  overflow: hidden;
  transition: border-color 0.2s ease-in-out;
  user-select: none;
}

.captcha-image:hover {
  border-color: #2d8cf0;
}

.captcha-image img {
  display: block;
  width: 120px;
  height: 40px;
  object-fit: cover;
}

.refresh-hint {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 12px;
  text-align: center;
  padding: 2px;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.captcha-image:hover .refresh-hint {
  opacity: 1;
}

.strength-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #f56c6c;
  transition: color 0.3s ease;
}

.strength-item.valid {
  color: #67c23a;
}

.strength-item .ivu-icon {
  font-size: 14px;
}
</style>
