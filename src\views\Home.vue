<template>
  <div style="background: #eee;width: 100%;float: left;">
    <!-- 用户信息栏padding-top: 56px -->
    <!-- <div class="user-header">
      <div class="user-info">
        <Icon type="ios-person" size="20" />
        <span>欢迎，{{ user ? user.username : "用户" }}</span>
      </div>
      <Button type="text" @click="handleLogout" class="logout-btn">
        <Icon type="ios-log-out" />
        退出登录
      </Button>
    </div> -->

    <Card
      title="工具列表"
      icon="ios-apps"
      :padding="10"
      shadow
      bordered
      style="width: 500px;margin-left: 20px; margin-top: 20px;;text-align: center"
    >
      <CellGroup>
        <Cell title="地图打点(高德地图)" :to="mapRouter('1.4.15')" />
        <Cell title="地图打点(高德地图V2.0)" :to="mapRouter('2.0')" />
        <!--                <Cell title="图表展示(echarts)" to="/chart"/>-->
        <!--                <Cell title="图表实时展示(echarts)" to="/chart-socket"/>-->
        <template v-if="fullEnv">
          <Cell title="打点统计图表" to="/map-chart" />
          <Cell title="数据比对(mysql)" to="/data-compare">
            <Badge text="beta" slot="extra" />
          </Cell>
          <Cell title="代码在线运行" to="/real-run">
            <Badge text="beta" slot="extra" />
          </Cell>
          <Cell title="DataOs工具" to="/data-os">
            <Badge text="beta" slot="extra" />
          </Cell>
        </template>
      </CellGroup>
    </Card>
    <!--        <Card title="时间戳转化" icon="ios-alarm" :padding="10" shadow bordered-->
    <!--              style="width: 500px;float: left;margin-left: 20px; margin-top: 20px">-->
    <!--            <Form :label-width="60">-->
    <!--                <FormItem label="时间戳" prop="ip" style="margin-bottom: 10px">-->
    <!--                    <Input type="text" v-model="timestamp" style="width: 220px" size="large"-->
    <!--                           icon="ios-clock-outline" @on-click="changeUnixFormat"></Input>-->
    <!--                    <Button type="primary" style="margin-left: 20px"-->
    <!--                            v-clipboard:copy="timestamp" v-clipboard:success="copySuccess">复制-->
    <!--                    </Button>-->
    <!--                </FormItem>-->
    <!--                <FormItem label="" prop="ip" style="margin-bottom: 10px;">-->
    <!--                    <Icon style="transform: rotate(90deg)" type="ios-swap" size="30"/>-->
    <!--                </FormItem>-->
    <!--                <FormItem label="日期">-->
    <!--                    <Input type="text" v-model="date" style="width: 220px" size="large"></Input>-->
    <!--                    <Button type="primary" style="margin-left: 20px"-->
    <!--                            v-clipboard:copy="date" v-clipboard:success="copySuccess">复制-->
    <!--                    </Button>-->
    <!--                </FormItem>-->
    <!--                <FormItem>-->
    <!--                    <Button type="primary" @click="resetTime">重置</Button>-->
    <!--                </FormItem>-->
    <!--            </Form>-->
    <!--        </Card>-->
    <Card
      title="点数据转化"
      icon="md-swap"
      :padding="10"
      shadow
      bordered
      style="margin: 20px; padding: 12px 12px 0 12px;"
    >
      <Badge text="beta" slot="extra" />
      <Row>
        <Col span="11">
          <Form :model="sourceFormItem" label-position="top">
            <FormItem
              label="原始类型"
              style="display: inline-block;width: 180px"
            >
              <RadioGroup v-model="dataType">
                <Radio label="point">海量点</Radio>
                <Radio label="polygon">轮廓</Radio>
                <Radio label="s2" disabled>S2</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem
              label="原始坐标系"
              style="display: inline-block;width: 229px"
            >
              <Select v-model="sourceFormItem.coordinate" filterable>
                <Option
                  v-for="item in coordinateList"
                  :value="item.value"
                  :key="item.value"
                  :label="item.label"
                >
                  <span>{{ item.label }}</span>
                  <div style="float:right;color:#515a6e;">{{ item.value }}</div>
                </Option>
              </Select>
            </FormItem>
            <FormItem
              label="原始格式"
              style="display: inline-block;width: 96px;margin-left: 24px"
            >
              <Select v-model="sourceFormItem.format" filterable>
                <Option value="double" label="浮点型"></Option>
                <Option value="int" label="整型"></Option>
                <Option value="blob" label="二进制"></Option>
                <Option value="wkt" label="WKT"></Option>
                <Option value="regex" label="正则"></Option>
              </Select>
            </FormItem>
            <FormItem>
              <Input
                v-model="sourceFormItem.sourceText"
                type="textarea"
                placeholder="经纬度/16进制轮廓"
                :rows="8"
              />
            </FormItem>
          </Form>
        </Col>
        <Col span="2">
          <div
            style="text-align: center;vertical-align: center;height: 280px;line-height: 280px"
          >
            <Button
              size="large"
              icon="md-arrow-round-forward"
              type="primary"
              shape="circle"
              @click="dataTransfer"
            ></Button>
          </div>
        </Col>
        <Col span="11">
          <Form :model="resultFormItem" label-position="top">
            <FormItem
              label="目标类型"
              style="display: inline-block;width: 180px"
            >
              <RadioGroup v-model="dataType">
                <Radio label="point">海量点</Radio>
                <Radio label="polygon">轮廓</Radio>
                <Radio label="s2" disabled>S2</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem
              label="目标坐标系"
              style="display: inline-block;width: 229px"
            >
              <Select v-model="resultFormItem.coordinate" filterable>
                <Option
                  v-for="item in coordinateList"
                  :value="item.value"
                  :key="item.value"
                  :label="item.label"
                >
                  <span>{{ item.label }}</span>
                  <div style="float:right;color:#515a6e;">{{ item.value }}</div>
                </Option>
              </Select>
            </FormItem>
            <FormItem
              label="目标格式"
              style="display: inline-block;width: 96px;margin-left: 24px"
            >
              <Select v-model="resultFormItem.format" filterable>
                <Option value="double" label="浮点型"></Option>
                <Option value="int" label="整型"></Option>
                <Option
                  value="blob"
                  :disabled="sourceFormItem.format === 'regex'"
                  label="二进制"
                ></Option>
                <Option
                  value="wkt"
                  :disabled="sourceFormItem.format === 'regex'"
                  label="WKT"
                ></Option>
              </Select>
            </FormItem>
            <FormItem>
              <Input
                v-model="resultFormItem.sourceText"
                type="textarea"
                placeholder="经纬度/16进制轮廓"
                :rows="8"
              />
            </FormItem>
          </Form>
        </Col>
      </Row>
    </Card>
    <Card
      title="区域生成"
      icon="md-swap"
      :padding="10"
      shadow
      bordered
      style="margin: 20px; padding: 12px 12px 0 12px;"
    >
      <Form :model="areaGenForm" label-position="top">
        <Row>
          <FormItem style="display: inline-block;width: 180px">
            <RadioGroup v-model="areaGenForm.type">
              <Radio label="circle">圆</Radio>
              <Radio label="grid">栅格</Radio>
              <Radio label="s2">S2</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem style="display: inline-block;width: 700px">
            <template v-if="areaGenForm.type === 'circle'">
              经度,纬度,半径(m)
            </template>
            <template v-else-if="areaGenForm.type === 'grid'">
              经度,纬度,栅格大小(m)
            </template>
            <template v-else-if="areaGenForm.type === 's2'">
              经度,纬度,S2等级
            </template>
            <InputNumber v-model="fastArg" style="width: 100px"></InputNumber>
          </FormItem>
        </Row>
        <Row>
          <Col span="11">
            <FormItem>
              <Input
                v-model="areaGenForm.sourceText"
                type="textarea"
                placeholder="经纬度点"
                :rows="8"
              />
            </FormItem>
          </Col>
          <Col span="2">
            <div
              style="text-align: center;vertical-align: center;height: 160px;line-height: 160px"
            >
              <Button
                size="large"
                icon="md-arrow-round-forward"
                type="primary"
                shape="circle"
                @click="genArea"
              ></Button>
            </div>
          </Col>
          <Col span="11">
            <FormItem>
              <Input
                v-model="areaGenForm.resultText"
                type="textarea"
                placeholder="经纬度轮廓"
                :rows="8"
              />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Card>
  </div>
</template>

<script>
import moment from "moment";
import { pointChange, polygonChange, fullEnv } from "../common/utils";
import { post } from "../api";
import { mapGetters, mapActions } from "vuex";

const DEFAULT_FORMAT = {
  date: "YYYY-MM-DD HH:mm:ss",
  unixSecond: "X",
  unixMilliSecond: "x",
};
export default {
  name: "home",
  computed: {
    ...mapGetters(["user"]),
    fullEnv() {
      return fullEnv;
    },
    mapRouter() {
      return function(version) {
        return {
          name: "map",
          params: {
            version: version,
          },
        };
      };
    },
  },
  components: {},
  data() {
    return {
      timestamp: "",
      date: "",
      unixFormat: DEFAULT_FORMAT.unixSecond,
      dataType: "point",
      sourceFormItem: {
        sourceText: "",
        coordinate: "WGS84",
        format: "double",
      },
      resultFormItem: {
        sourceText: "",
        coordinate: "WGS84",
        format: "double",
      },
      coordinateList: [
        {
          label: "GPS地心坐标系",
          value: "WGS84",
        },
        {
          label: "火星坐标系(腾讯/高德)",
          value: "GCJ02",
        },
        {
          label: "百度坐标系(百度)",
          value: "BD09",
        },
      ],
      areaGenForm: {
        type: "circle",
        sourceText: "",
        resultText: "",
      },
      fastArg: 1,
    };
  },
  mounted() {
    this.date = moment(new Date()).format(DEFAULT_FORMAT.date);
  },
  watch: {
    timestamp(newName) {
      this.date = moment(newName, this.unixFormat).format(DEFAULT_FORMAT.date);
    },
    date(newName) {
      this.timestamp = moment(newName, DEFAULT_FORMAT.date).format(
        this.unixFormat
      );
    },
  },
  methods: {
    ...mapActions(["logout"]),

    // 退出登录
    handleLogout() {
      this.$Modal.confirm({
        title: "确认退出",
        content: "确定要退出登录吗？",
        onOk: () => {
          this.logout();
          this.$Message.success("已退出登录");
          this.$router.push("/login");
        },
      });
    },

    changeUnixFormat: function() {
      if (this.unixFormat === DEFAULT_FORMAT.unixSecond) {
        this.unixFormat = DEFAULT_FORMAT.unixMilliSecond;
      } else {
        this.unixFormat = DEFAULT_FORMAT.unixSecond;
      }
      this.timestamp = moment(this.date, DEFAULT_FORMAT.date).format(
        this.unixFormat
      );
    },
    resetTime: function() {
      this.date = moment(new Date()).format(DEFAULT_FORMAT.date);
    },
    copySuccess: function() {
      this.$Message.info("已复制到剪贴板");
    },
    dataTransfer: function() {
      let list = this.sourceFormItem.sourceText.split("\n");
      if (this.dataType === "point") {
        this.resultFormItem.sourceText = pointChange(
          list,
          this.sourceFormItem.coordinate,
          this.sourceFormItem.format,
          this.resultFormItem.coordinate,
          this.resultFormItem.format
        );
      } else if (this.dataType === "polygon") {
        this.resultFormItem.sourceText = polygonChange(
          list,
          this.sourceFormItem.coordinate,
          this.sourceFormItem.format,
          this.resultFormItem.coordinate,
          this.resultFormItem.format
        );
      }
    },
    genArea: function() {
      let formData = this.areaGenForm;
      let fullText = this.addFastArgText();
      formData.file = new Blob([fullText], { type: "text/html;charset=utf-8" });
      post("/coordinate/area/gen", formData).then((response) => {
        if (response.data.code === 1) {
          let resultText = response.data.data.list
            .map((a) => {
              return a.pointList
                .map((polygon) =>
                  polygon
                    .map((m) => {
                      return m["wgsLng"] + "," + m["wgsLat"];
                    })
                    .join(";")
                )
                .join(" ");
            })
            .join("\n");
          this.areaGenForm.resultText = resultText;
        }
      });
    },
    addFastArgText: function() {
      let delimiter = ",";
      let list = this.areaGenForm.sourceText.split("\n");
      return list
        .map((line) => {
          let splits = line.split(delimiter, -1);
          if (splits != null && splits.length >= 2) {
            return splits[0] + delimiter + splits[1] + delimiter + this.fastArg;
          } else {
            return "";
          }
        })
        .join("\n");
    },
  },
};
</script>

<style scoped>
.user-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 0;
  z-index: 999;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
}

.logout-btn {
  color: #666;
  padding: 4px 8px;
}

.logout-btn:hover {
  color: #2d8cf0;
}
</style>
