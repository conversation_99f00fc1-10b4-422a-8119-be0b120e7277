import Vue from "vue";
import VueRouter from "vue-router";
import Map from "../views/Map";
import Home from "../views/Home";
import Chart from "../views/Chart";
import ChartSocket from "../views/ChartSocket";
import DataCompare from "../views/DataCompare";
import MapChart from "../views/MapChart";
import RealRun from "../views/RealRun";
import DataOs from "../views/DataOs.vue";
import LoginPage from "../views/LoginPage.vue";
import store from "../store";

Vue.use(VueRouter);

const routes = [
  {
    path: "/login",
    name: "login",
    component: LoginPage,
    meta: {
      title: "用户登录",
      requiresAuth: false,
    },
  },
  {
    path: "/",
    name: "home",
    component: Home,
    meta: {
      title: "upos-工具列表",
      requiresAuth: true,
    },
  },
  {
    path: "/map",
    name: "map",
    component: Map,
    meta: {
      title: "upos-打点工具",
      requiresAuth: true,
    },
  },
  {
    path: "/map/:key",
    name: "map-key",
    component: Map,
    meta: {
      title: "upos-打点工具",
      requiresAuth: true,
    },
  },
  {
    path: "/map-chart",
    name: "mapChart",
    component: MapChart,
    meta: {
      title: "upos-打点工具-图表",
      requiresAuth: true,
    },
  },
  {
    path: "/chart",
    name: "chart",
    component: Chart,
    meta: {
      title: "upos-图表",
      requiresAuth: true,
    },
  },
  {
    path: "/chart-socket",
    name: "chartSocket",
    component: ChartSocket,
    meta: {
      title: "upos-图表",
      requiresAuth: true,
    },
  },
  {
    path: "/data-compare",
    name: "dataCompare",
    component: DataCompare,
    meta: {
      title: "upos-数据比对",
      requiresAuth: true,
    },
  },
  {
    path: "/real-run",
    name: "realRun",
    component: RealRun,
    meta: {
      title: "upos-代码在线运行",
      requiresAuth: true,
    },
  },
  {
    path: "/data-os",
    name: "dataOs",
    component: DataOs,
    meta: {
      title: "upos-DataOs",
      requiresAuth: true,
    },
  },
];

const router = new VueRouter({
  routes,
});

store;
// 路由守卫
// router.beforeEach((to, from, next) => {
//   // 初始化认证状态
//   store.dispatch("initializeAuth");

//   // 检查路由是否需要认证
//   const requiresAuth = to.matched.some((record) => record.meta.requiresAuth);
//   const isLoggedIn = store.getters.isLoggedIn;

//   if (requiresAuth && !isLoggedIn) {
//     next({
//       path: "/login",
//       query: { redirect: to.fullPath },
//     });
//   } else if (to.path === "/login" && isLoggedIn) {
//     next("/");
//   } else {
//     next();
//   }
// });

router.afterEach((to) => {
  if (to.meta.title) {
    document.title = to.meta.title;
  }
});

export default router;
